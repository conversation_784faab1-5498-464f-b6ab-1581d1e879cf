# MCP SSE 标签过滤功能使用说明

## 概述

新增了支持标签过滤的 MCP SSE 路由，允许客户端根据特定标签过滤工具。

## 新增路由

### 1. 原有路由（无过滤）
```
GET /sse
```
返回所有启用的工具，无标签过滤。

### 2. 新增路由（标签过滤）
```
GET /sse-{tag}
```
返回指定标签的启用工具。

**参数说明：**
- `{tag}`: 标签名称，用于过滤工具

## 使用示例

### 示例 1: 获取所有工具
```bash
curl -N -H "Accept: text/event-stream" http://localhost:8000/sse
```

### 示例 2: 获取特定标签的工具
假设有一个名为 "database" 的标签：
```bash
curl -N -H "Accept: text/event-stream" http://localhost:8000/sse-database
```

### 示例 3: 获取 "api" 标签的工具
```bash
curl -N -H "Accept: text/event-stream" http://localhost:8000/sse-api
```

## 功能特性

1. **标签验证**: 如果指定的标签不存在，将返回空的工具列表
2. **启用状态过滤**: 只返回启用状态的工具
3. **MCP 格式**: 返回的工具符合 MCP (Model Context Protocol) 标准格式
4. **日志记录**: 详细的日志记录，包括过滤信息和工具数量

## 实现细节

### 核心函数
- `list_tools(tag_name: Optional[str] = None)`: 根据标签名称过滤工具
- `set_tag_filter(tag_name: Optional[str])`: 设置当前标签过滤器
- `handle_sse_endpoint_with_tag(tag: str, ...)`: 处理带标签的 SSE 连接

### 数据库查询
使用 `ToolService.query_tools()` 方法的 `tag_ids` 参数进行过滤：
```python
# 根据标签名称获取标签ID
tag_service = TagService(db_session)
tag = await tag_service.get_tag_by_name(tag_name)
if tag:
    tag_ids = [tag.id]
    
# 查询工具
tools, _ = await tool_service.query_tools(page=1, size=10000, tag_ids=tag_ids)
```

## 错误处理

1. **标签不存在**: 返回空工具列表，记录警告日志
2. **数据库连接错误**: 返回空工具列表，记录错误日志
3. **工具转换错误**: 跳过有问题的工具，继续处理其他工具

## 日志示例

```
INFO - Filtering tools by tag: database (ID: 1)
INFO - Loaded 3 enabled tools for tag 'database'
WARNING - Tag not found: nonexistent_tag
```

## 测试

可以使用提供的测试脚本验证功能：
```bash
python test_tag_filter.py
```

## 注意事项

1. 标签名称区分大小写
2. 只返回启用状态的工具
3. 如果标签不存在，返回空列表而不是错误
4. 支持与现有 MCP 客户端的完全兼容性

## 相关文件

- `api/routers/mcp_sse_router.py`: 主要实现文件
- `api/services/tool_service.py`: 工具查询服务
- `api/services/tag_service.py`: 标签查询服务
- `test_tag_filter.py`: 功能测试脚本
