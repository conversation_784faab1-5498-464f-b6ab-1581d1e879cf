"""
MCP SSE router for FastAPI using transport-based implementation.
"""

import json
import logging
from typing import Dict, Any, List, Optional

import mcp.types as types
from mcp.server.sse import SseServerTransport
from fastapi import APIRouter, Request, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from api.database import get_db
from api.services.tool_service import ToolService

# Create logger
logger = logging.getLogger(__name__)

# Create FastAPI router
router = APIRouter(tags=["mcp-sse"])

# Initialize MCP transport only
mcp_sse_transport = SseServerTransport("/messages/")

# Global database session for MCP handlers
_db_session: Optional[AsyncSession] = None


def set_db_session(db: AsyncSession):
    """Set the database session for MCP handlers."""
    global _db_session
    _db_session = db


async def list_tools() -> List[types.Tool]:
    """Get all enabled tools from database and convert to MCP Tool format."""
    if not _db_session:
        logger.error("Database session not available")
        return []

    try:
        tool_service = ToolService(_db_session)
        tools, _ = await tool_service.query_tools(page=1, size=10000)

        # Filter enabled tools and convert to MCP format
        mcp_tools = []
        for tool in tools:
            if tool.is_enabled:
                try:
                    # Parse parameters JSON Schema
                    parameters = json.loads(tool.parameters) if tool.parameters else {}

                    mcp_tool = types.Tool(
                        name=tool.name,
                        description=tool.description or "",
                        inputSchema=parameters,
                    )
                    mcp_tools.append(mcp_tool)
                except Exception as e:
                    logger.error(f"Error converting tool {tool.name}: {e}")
                    continue

        logger.info(f"Loaded {len(mcp_tools)} enabled tools")
        return mcp_tools

    except Exception as e:
        logger.error(f"Error getting enabled tools: {e}")
        return []


async def execute_tool(name: str, arguments: Dict[str, Any]) -> List[types.TextContent]:
    """Execute a tool by name with given arguments."""
    if not _db_session:
        logger.error("Database session not available")
        return [types.TextContent(type="text", text="Database session not available")]

    try:
        tool_service = ToolService(_db_session)

        # Get tool by name
        tool = await tool_service.get_tool_by_name(name)
        if not tool:
            return [types.TextContent(type="text", text=f"Tool '{name}' not found")]

        if not tool.is_enabled:
            return [types.TextContent(type="text", text=f"Tool '{name}' is disabled")]

        # Execute tool
        result, logs = await tool_service.execute_tool(tool.id, arguments, call_type="mcp")

        # Format result as text
        if result is not None:
            result_text = json.dumps(result, ensure_ascii=False, indent=2) if isinstance(result, (dict, list)) else str(result)
        else:
            result_text = "No result returned"

        return [types.TextContent(type="text", text=result_text)]

    except Exception as e:
        logger.error(f"Error executing tool {name}: {e}")
        return [types.TextContent(type="text", text=f"Error executing tool: {str(e)}")]


# Register MCP server handlers
@mcp_sse_server.list_tools()
async def mcp_sse_list_tools() -> List[types.Tool]:
    """List available tools."""
    return await list_tools()


@mcp_sse_server.call_tool()
async def mcp_sse_call_tool(name: str, arguments: Dict[str, Any]) -> List[types.TextContent]:
    """Handle tool call."""
    return await execute_tool(name, arguments)


# FastAPI endpoints
@router.get("/sse")
async def handle_sse_endpoint(request: Request, db: AsyncSession = Depends(get_db)):
    """Handle SSE connection for MCP."""
    # Set database session for MCP handlers
    set_db_session(db)

    # Create a context manager for the SSE connection
    async with mcp_sse_transport.connect_sse(
        request.scope,
        request.receive,
        request._send
    ) as streams:
        # Run the MCP server with the streams
        await mcp_sse_server.run(
            streams[0],  # read stream
            streams[1],  # write stream
            mcp_sse_server.create_initialization_options(),
        )


@router.post("/messages/{path:path}")
async def handle_post_messages(request: Request, db: AsyncSession = Depends(get_db)):
    """Handle POST messages for MCP."""
    # Set database session for MCP handlers
    set_db_session(db)

    # Use the transport's handle_post_message ASGI application
    await mcp_sse_transport.handle_post_message(
        request.scope,
        request.receive,
        request._send
    )
