"""
MCP SSE router for FastAPI using transport-based implementation.
"""

import json
import logging
from typing import Dict, Any, List, Optional

import mcp.types as types
from mcp.server.lowlevel import Server
from mcp.server.sse import SseServerTransport
from fastapi import APIRouter, Request, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from api.database import get_db
from api.services.tool_service import ToolService
from api.services.tag_service import TagService

# Create logger
logger = logging.getLogger(__name__)

# Create FastAPI router
router = APIRouter(tags=["mcp-sse"])

# Initialize MCP transport only
mcp_sse_transport = SseServerTransport("/messages/")


class MCPServerHandler:
    """MCP Server handler with database session and tag filter."""

    def __init__(self, db_session: AsyncSession, tag_filter: Optional[str] = None):
        """Initialize handler with database session and optional tag filter.

        Args:
            db_session: Database session
            tag_filter: Optional tag name to filter tools by
        """
        self.db_session = db_session
        self.tag_filter = tag_filter

    async def list_tools(self) -> List[types.Tool]:
        """Get all enabled tools from database and convert to MCP Tool format."""
        try:
            tool_service = ToolService(self.db_session)

            # Get tag IDs if tag_filter is provided
            tag_ids = None
            if self.tag_filter:
                tag_service = TagService(self.db_session)
                tag = await tag_service.get_tag_by_name(self.tag_filter)
                if tag:
                    tag_ids = [tag.id]
                    logger.info(f"Filtering tools by tag: {self.tag_filter} (ID: {tag.id})")
                else:
                    logger.warning(f"Tag not found: {self.tag_filter}")
                    return []  # Return empty list if tag doesn't exist

            tools, _ = await tool_service.query_tools(page=1, size=10000, tag_ids=tag_ids)

            # Filter enabled tools and convert to MCP format
            mcp_tools = []
            for tool in tools:
                if tool.is_enabled:
                    try:
                        # Parse parameters JSON Schema
                        parameters = json.loads(tool.parameters) if tool.parameters else {}

                        mcp_tool = types.Tool(
                            name=tool.name,
                            description=tool.description or "",
                            inputSchema=parameters,
                        )
                        mcp_tools.append(mcp_tool)
                    except Exception as e:
                        logger.error(f"Error converting tool {tool.name}: {e}")
                        continue

            if self.tag_filter:
                logger.info(f"Loaded {len(mcp_tools)} enabled tools for tag '{self.tag_filter}'")
            else:
                logger.info(f"Loaded {len(mcp_tools)} enabled tools")
            return mcp_tools

        except Exception as e:
            logger.error(f"Error getting enabled tools: {e}")
            return []

    async def execute_tool(self, name: str, arguments: Dict[str, Any]) -> List[types.TextContent]:
        """Execute a tool by name with given arguments."""
        try:
            tool_service = ToolService(self.db_session)

            # Get tool by name
            tool = await tool_service.get_tool_by_name(name)
            if not tool:
                return [types.TextContent(type="text", text=f"Tool '{name}' not found")]

            if not tool.is_enabled:
                return [types.TextContent(type="text", text=f"Tool '{name}' is disabled")]

            # Execute tool
            result, logs = await tool_service.execute_tool(tool.id, arguments, call_type="mcp")

            # Format result as text
            if result is not None:
                result_text = json.dumps(result, ensure_ascii=False, indent=2) if isinstance(result, (dict, list)) else str(result)
            else:
                result_text = "No result returned"

            return [types.TextContent(type="text", text=result_text)]

        except Exception as e:
            logger.error(f"Error executing tool {name}: {e}")
            return [types.TextContent(type="text", text=f"Error executing tool: {str(e)}")]


def create_mcp_server(handler: MCPServerHandler) -> Server:
    """Create a new MCP server instance with the given handler.

    Args:
        handler: MCPServerHandler instance

    Returns:
        Server: Configured MCP server
    """
    server = Server("Easy MCP SSE Server")

    @server.list_tools()
    async def list_tools() -> List[types.Tool]:
        """List available tools."""
        return await handler.list_tools()

    @server.call_tool()
    async def call_tool(name: str, arguments: Dict[str, Any]) -> List[types.TextContent]:
        """Handle tool call."""
        return await handler.execute_tool(name, arguments)

    return server


# FastAPI endpoints
@router.get("/sse")
async def handle_sse_endpoint(request: Request, db: AsyncSession = Depends(get_db)):
    """Handle SSE connection for MCP."""
    # Create handler without tag filter
    handler = MCPServerHandler(db)

    # Create MCP server instance for this connection
    mcp_server = create_mcp_server(handler)

    # Create a context manager for the SSE connection
    async with mcp_sse_transport.connect_sse(
        request.scope,
        request.receive,
        request._send
    ) as streams:
        # Run the MCP server with the streams
        await mcp_server.run(
            streams[0],  # read stream
            streams[1],  # write stream
            mcp_server.create_initialization_options(),
        )


@router.get("/sse-{tag}")
async def handle_sse_endpoint_with_tag(tag: str, request: Request, db: AsyncSession = Depends(get_db)):
    """Handle SSE connection for MCP with tag filtering.

    Args:
        tag: Tag name to filter tools by
    """
    # Create handler with tag filter
    handler = MCPServerHandler(db, tag_filter=tag)

    # Create MCP server instance for this connection
    mcp_server = create_mcp_server(handler)

    # Create a context manager for the SSE connection
    async with mcp_sse_transport.connect_sse(
        request.scope,
        request.receive,
        request._send
    ) as streams:
        # Run the MCP server with the streams
        await mcp_server.run(
            streams[0],  # read stream
            streams[1],  # write stream
            mcp_server.create_initialization_options(),
        )


@router.post("/messages/{path:path}")
async def handle_post_messages(request: Request, db: AsyncSession = Depends(get_db)):
    """Handle POST messages for MCP."""
    # Use the transport's handle_post_message ASGI application
    await mcp_sse_transport.handle_post_message(
        request.scope,
        request.receive,
        request._send
    )
