"""
MCP Streamable HTTP router for FastAPI using transport-based implementation.

This module provides Streamable HTTP endpoints for the Model Context Protocol (MCP).
It supports both general tool listing and tag-filtered tool listing with concurrent-safe design.

Features:
- Concurrent-safe design with per-connection handlers
- Automatic pagination for large tool sets
- Tag-based tool filtering
- Comprehensive error handling and logging
- Streamable HTTP transport for better performance
"""

import json
import logging
from typing import Dict, Any, List, Optional

import mcp.types as types
from fastapi import APIRouter, Request, Depends
from mcp.server.lowlevel import Server
from mcp.server.streamable_http import StreamableHTTPServerTransport
from sqlalchemy.ext.asyncio import AsyncSession

from api.database import get_db
from api.services.tag_service import TagService
from api.services.tool_service import ToolService

# Constants
DEFAULT_PAGE_SIZE = 100  # Optimal page size for memory management
MCP_SERVER_NAME = "Easy MCP Streamable HTTP Server"

# Create logger
logger = logging.getLogger(__name__)

# Create FastAPI router
router = APIRouter(tags=["mcp-stream"])

# Initialize MCP transport
mcp_stream_transport = StreamableHTTPServerTransport()