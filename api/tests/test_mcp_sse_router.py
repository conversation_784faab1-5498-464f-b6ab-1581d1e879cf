"""
Test MCP SSE router functionality.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from api.main import app
from api.routers.mcp_sse_router import list_tools, set_db_session, set_tag_filter
from api.models.tb_tool import TbTool
from api.models.tb_tag import TbTag


@pytest.fixture
def mock_db_session():
    """Create a mock database session."""
    return AsyncMock(spec=AsyncSession)


@pytest.fixture
def mock_tool():
    """Create a mock tool."""
    tool = MagicMock(spec=TbTool)
    tool.id = 1
    tool.name = "test_tool"
    tool.description = "Test tool description"
    tool.parameters = '{"type": "object", "properties": {}}'
    tool.is_enabled = True
    return tool


@pytest.fixture
def mock_tag():
    """Create a mock tag."""
    tag = MagicMock(spec=TbTag)
    tag.id = 1
    tag.name = "test_tag"
    tag.description = "Test tag description"
    return tag


class TestMCPSSERouter:
    """Test MCP SSE router functionality."""

    @pytest.mark.asyncio
    async def test_list_tools_without_tag_filter(self, mock_db_session, mock_tool):
        """Test listing tools without tag filter."""
        # Set up mock database session
        set_db_session(mock_db_session)
        
        # Mock ToolService
        with patch('api.routers.mcp_sse_router.ToolService') as mock_tool_service:
            mock_service_instance = mock_tool_service.return_value
            mock_service_instance.query_tools.return_value = ([mock_tool], 1)
            
            # Call list_tools without tag filter
            result = await list_tools()
            
            # Verify results
            assert len(result) == 1
            assert result[0].name == "test_tool"
            assert result[0].description == "Test tool description"
            
            # Verify query_tools was called with correct parameters
            mock_service_instance.query_tools.assert_called_once_with(
                page=1, size=10000, tag_ids=None
            )

    @pytest.mark.asyncio
    async def test_list_tools_with_tag_filter(self, mock_db_session, mock_tool, mock_tag):
        """Test listing tools with tag filter."""
        # Set up mock database session
        set_db_session(mock_db_session)
        
        # Mock ToolService and TagService
        with patch('api.routers.mcp_sse_router.ToolService') as mock_tool_service, \
             patch('api.routers.mcp_sse_router.TagService') as mock_tag_service:
            
            mock_tool_service_instance = mock_tool_service.return_value
            mock_tool_service_instance.query_tools.return_value = ([mock_tool], 1)
            
            mock_tag_service_instance = mock_tag_service.return_value
            mock_tag_service_instance.get_tag_by_name.return_value = mock_tag
            
            # Call list_tools with tag filter
            result = await list_tools("test_tag")
            
            # Verify results
            assert len(result) == 1
            assert result[0].name == "test_tool"
            
            # Verify services were called with correct parameters
            mock_tag_service_instance.get_tag_by_name.assert_called_once_with("test_tag")
            mock_tool_service_instance.query_tools.assert_called_once_with(
                page=1, size=10000, tag_ids=[1]
            )

    @pytest.mark.asyncio
    async def test_list_tools_with_nonexistent_tag(self, mock_db_session):
        """Test listing tools with nonexistent tag filter."""
        # Set up mock database session
        set_db_session(mock_db_session)
        
        # Mock TagService to return None for nonexistent tag
        with patch('api.routers.mcp_sse_router.TagService') as mock_tag_service:
            mock_tag_service_instance = mock_tag_service.return_value
            mock_tag_service_instance.get_tag_by_name.return_value = None
            
            # Call list_tools with nonexistent tag
            result = await list_tools("nonexistent_tag")
            
            # Verify empty result
            assert len(result) == 0
            
            # Verify tag service was called
            mock_tag_service_instance.get_tag_by_name.assert_called_once_with("nonexistent_tag")

    @pytest.mark.asyncio
    async def test_list_tools_with_disabled_tool(self, mock_db_session):
        """Test listing tools filters out disabled tools."""
        # Set up mock database session
        set_db_session(mock_db_session)
        
        # Create a disabled tool
        disabled_tool = MagicMock(spec=TbTool)
        disabled_tool.id = 1
        disabled_tool.name = "disabled_tool"
        disabled_tool.is_enabled = False
        
        # Mock ToolService
        with patch('api.routers.mcp_sse_router.ToolService') as mock_tool_service:
            mock_service_instance = mock_tool_service.return_value
            mock_service_instance.query_tools.return_value = ([disabled_tool], 1)
            
            # Call list_tools
            result = await list_tools()
            
            # Verify disabled tool is filtered out
            assert len(result) == 0

    def test_set_tag_filter(self):
        """Test setting tag filter."""
        # Test setting tag filter
        set_tag_filter("test_tag")
        from api.routers.mcp_sse_router import _current_tag_filter
        assert _current_tag_filter == "test_tag"
        
        # Test clearing tag filter
        set_tag_filter(None)
        assert _current_tag_filter is None
