# MCP Streamable HTTP 支持使用说明

## 概述

基于 `mcp_sse_router.py` 的实现，新增了 MCP Streamable HTTP 支持，提供更高效的 HTTP 传输方式。该实现支持标签过滤、并发安全设计和自动分页功能。

## 新增路由

### 1. 通用路由（无过滤）
```
POST /stream
```
返回所有启用的工具，无标签过滤。

### 2. 标签过滤路由
```
POST /stream-{tag}
```
返回指定标签的启用工具。

**参数说明：**
- `{tag}`: 标签名称，用于过滤工具

## 使用示例

### 示例 1: 获取所有工具
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc": "2.0", "method": "tools/list", "id": 1}' \
  http://localhost:8000/stream
```

### 示例 2: 获取特定标签的工具
假设有一个名为 "database" 的标签：
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc": "2.0", "method": "tools/list", "id": 1}' \
  http://localhost:8000/stream-database
```

### 示例 3: 执行工具
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0", 
    "method": "tools/call", 
    "params": {
      "name": "tool_name",
      "arguments": {"param1": "value1"}
    },
    "id": 2
  }' \
  http://localhost:8000/stream
```

## 功能特性

1. **Streamable HTTP 传输**: 使用 MCP 的 Streamable HTTP 传输协议，提供更好的性能
2. **标签过滤**: 支持根据标签名称过滤工具
3. **并发安全**: 每个连接创建独立的处理器和传输实例
4. **自动分页**: 循环分页获取所有工具，确保不遗漏
5. **错误处理**: 完整的错误处理和标准化响应
6. **简化实现**: 避免复杂的服务器生命周期管理

## 实现细节

### 核心类和函数
- `MCPStreamServerHandler`: 处理 MCP Streamable HTTP 请求的核心类
- `create_mcp_stream_server(handler)`: 为每个连接创建独立的 MCP 服务器实例
- `handle_stream_endpoint()`: 处理通用 Streamable HTTP 连接
- `handle_stream_endpoint_with_tag()`: 处理带标签过滤的 Streamable HTTP 连接

### 与 SSE 实现的对比

| 特性 | SSE 实现 | Streamable HTTP 实现 |
|------|----------|---------------------|
| 传输协议 | Server-Sent Events | Streamable HTTP |
| HTTP 方法 | GET | POST |
| 连接方式 | 长连接流 | 请求-响应 |
| 性能 | 适合实时推送 | 适合请求-响应场景 |
| 复杂度 | 较简单 | 简单（已简化） |
| 初始化问题 | 较少 | 已解决 |

### 正确的实现方式
```python
@router.post("/stream")
async def handle_stream_endpoint(request: Request, db: AsyncSession = Depends(get_db)):
    # 创建处理器
    handler = MCPStreamServerHandler(db)

    # 创建 MCP 服务器实例
    mcp_server = create_mcp_stream_server(handler)

    # 创建传输实例
    transport = StreamableHTTPServerTransport(mcp_session_id=None)

    # 建立连接并处理请求
    async with transport.connect() as streams:
        # 在后台启动 MCP 服务器
        import asyncio
        server_task = asyncio.create_task(
            mcp_server.run(
                streams[0],  # read stream
                streams[1],  # write stream
                mcp_server.create_initialization_options(),
            )
        )

        try:
            # 处理 HTTP 请求
            await transport.handle_request(
                request.scope,
                request.receive,
                request._send
            )
        finally:
            # 清理服务器任务
            if not server_task.done():
                server_task.cancel()
                try:
                    await server_task
                except asyncio.CancelledError:
                    pass
```

### 并发安全设计
```python
# 为每个请求创建独立的处理器
handler = MCPStreamServerHandler(db, tag_filter=tag)

# 为每个请求创建独立的 MCP 服务器实例
mcp_server = create_mcp_stream_server(handler)

# 为每个请求创建独立的传输实例
transport = StreamableHTTPServerTransport(mcp_session_id=None)
```

## 解决的问题

### 连接错误修复
解决了以下关键错误：

1. **"No read stream writer available"错误**：
   - 原因：在调用 `handle_request()` 之前没有调用 `connect()`
   - 解决：正确使用 `async with transport.connect()` 建立连接

2. **初始化警告修复**：
   - 通过正确的服务器启动顺序避免初始化问题
   - 使用异步任务管理确保服务器正确运行

### 错误处理改进
- 统一的错误处理机制
- 标准化的错误响应格式
- 完整的异常捕获和日志记录

## 错误处理

1. **标签不存在**: 返回空工具列表，记录警告日志
2. **数据库连接错误**: 返回空工具列表，记录错误日志
3. **工具转换错误**: 跳过有问题的工具，继续处理其他工具
4. **工具执行错误**: 返回标准化错误响应
5. **传输错误**: 由 MCP 传输层处理

## 测试

### 单元测试
```bash
# 运行单元测试
cd api && python -m pytest tests/test_mcp_stream_router.py -v
```

### 功能测试
基本功能测试确认：
- ✅ 处理器和服务器创建正常
- ✅ 传输实例创建正常
- ✅ 连接建立成功（返回2个流）
- ✅ 服务器任务启动正常
- ✅ 标签过滤功能正常
- ✅ 任务清理机制正常
- ✅ 无连接错误和初始化警告

### 启动服务器进行测试
```bash
cd api && uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## 使用场景

### 适合 Streamable HTTP 的场景：
- 请求-响应模式的工具调用
- 需要更好的 HTTP 缓存支持
- 客户端不支持 SSE 的情况
- 需要标准 HTTP 状态码的场景

### 适合 SSE 的场景：
- 需要实时推送的场景
- 长连接监听工具状态变化
- 客户端支持 EventSource 的情况

## 注意事项

1. **正确的连接顺序**: 必须先调用 `transport.connect()` 再调用 `handle_request()`
2. **服务器任务管理**: 需要正确启动和清理 MCP 服务器任务
3. **HTTP 方法**: 使用 POST 方法，符合 MCP Streamable HTTP 规范
4. **标签名称区分大小写**
5. **只返回启用状态的工具**
6. **如果标签不存在，返回空列表而不是错误**
7. **支持与现有 MCP 客户端的完全兼容性**
8. **每个请求都有独立的处理器、服务器和传输实例**

## 相关文件

- `api/routers/mcp_stream_router.py`: 主要实现文件（已简化）
- `api/routers/mcp_sse_router.py`: SSE 实现参考
- `api/services/tool_service.py`: 工具查询服务
- `api/services/tag_service.py`: 标签查询服务
- `api/tests/test_mcp_stream_router.py`: 单元测试套件

## 集成到主应用

路由器已自动集成到主应用中：
```python
# api/main.py
from api.routers import mcp_stream_router
app.include_router(mcp_stream_router.router)
```

现在您可以通过以下端点访问 MCP Streamable HTTP 服务：
- `POST /stream` - 所有工具
- `POST /stream-{tag}` - 标签过滤的工具

## 性能优化

1. **简化架构**: 移除了复杂的异步任务管理
2. **直接处理**: 传输层直接处理 HTTP 请求
3. **资源隔离**: 每个请求独立的资源实例
4. **内存管理**: 自动分页和资源清理

这个简化的实现解决了初始化警告问题，提供了更稳定和可靠的 MCP Streamable HTTP 支持。
