# MCP Streamable HTTP 支持使用说明

## 概述

基于 `mcp_sse_router.py` 的实现，新增了 MCP Streamable HTTP 支持，提供更高效的 HTTP 传输方式。该实现支持标签过滤、并发安全设计和自动分页功能。

## 新增路由

### 1. 通用路由（无过滤）
```
POST /stream
```
返回所有启用的工具，无标签过滤。

### 2. 标签过滤路由
```
POST /stream-{tag}
```
返回指定标签的启用工具。

**参数说明：**
- `{tag}`: 标签名称，用于过滤工具

## 使用示例

### 示例 1: 获取所有工具
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc": "2.0", "method": "tools/list", "id": 1}' \
  http://localhost:8000/stream
```

### 示例 2: 获取特定标签的工具
假设有一个名为 "database" 的标签：
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc": "2.0", "method": "tools/list", "id": 1}' \
  http://localhost:8000/stream-database
```

### 示例 3: 执行工具
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0", 
    "method": "tools/call", 
    "params": {
      "name": "tool_name",
      "arguments": {"param1": "value1"}
    },
    "id": 2
  }' \
  http://localhost:8000/stream
```

## 功能特性

1. **Streamable HTTP 传输**: 使用 MCP 的 Streamable HTTP 传输协议，提供更好的性能
2. **标签过滤**: 支持根据标签名称过滤工具
3. **并发安全**: 每个连接创建独立的处理器和传输实例
4. **自动分页**: 循环分页获取所有工具，确保不遗漏
5. **错误处理**: 完整的错误处理和标准化响应
6. **会话管理**: 为每个连接创建独立的传输实例，确保会话隔离

## 实现细节

### 核心类和函数
- `MCPStreamServerHandler`: 处理 MCP Streamable HTTP 请求的核心类
- `create_mcp_stream_server(handler)`: 为每个连接创建独立的 MCP 服务器实例
- `handle_stream_endpoint()`: 处理通用 Streamable HTTP 连接
- `handle_stream_endpoint_with_tag()`: 处理带标签过滤的 Streamable HTTP 连接
- `_handle_stream_connection()`: 通用连接处理函数

### 与 SSE 实现的对比

| 特性 | SSE 实现 | Streamable HTTP 实现 |
|------|----------|---------------------|
| 传输协议 | Server-Sent Events | Streamable HTTP |
| HTTP 方法 | GET | POST |
| 连接方式 | 长连接流 | 请求-响应 |
| 性能 | 适合实时推送 | 适合请求-响应场景 |
| 复杂度 | 较简单 | 中等 |
| 会话管理 | 全局传输实例 | 每连接独立传输实例 |

### 并发安全设计
```python
# 为每个连接创建独立的处理器
handler = MCPStreamServerHandler(db, tag_filter=tag)

# 为每个连接创建独立的 MCP 服务器实例
mcp_server = create_mcp_stream_server(handler)

# 为每个连接创建独立的传输实例
transport = StreamableHTTPServerTransport(mcp_session_id=None)
```

### 传输实例管理
与 SSE 实现不同，Streamable HTTP 为每个连接创建独立的传输实例：
```python
async def _handle_stream_connection(request: Request, mcp_server: Server):
    # 为每个连接创建新的传输实例
    transport = StreamableHTTPServerTransport(mcp_session_id=None)
    
    async with transport.connect(
        request.scope,
        request.receive,
        request._send
    ) as streams:
        await mcp_server.run(streams[0], streams[1], 
                           mcp_server.create_initialization_options())
```

## 修复说明

**重要修复**: 原始实现中使用了错误的方法名 `connect_http`，正确的方法名是 `connect`。

```python
# 错误的用法
async with transport.connect_http(...) as streams:

# 正确的用法  
async with transport.connect(...) as streams:
```

## 错误处理

1. **标签不存在**: 返回空工具列表，记录警告日志
2. **数据库连接错误**: 返回空工具列表，记录错误日志
3. **工具转换错误**: 跳过有问题的工具，继续处理其他工具
4. **工具执行错误**: 返回标准化错误响应
5. **传输错误**: 由 MCP 传输层处理

## 测试

### 单元测试
```bash
# 运行单元测试
cd api && python -m pytest tests/test_mcp_stream_router.py -v
```

### 功能测试
```bash
# 测试基本功能
python test_stream_fix.py

# 测试 HTTP 端点（需要服务器运行）
python test_stream_endpoint.py
```

### 启动服务器进行测试
```bash
cd api && uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## 使用场景

### 适合 Streamable HTTP 的场景：
- 请求-响应模式的工具调用
- 需要更好的 HTTP 缓存支持
- 客户端不支持 SSE 的情况
- 需要标准 HTTP 状态码的场景

### 适合 SSE 的场景：
- 需要实时推送的场景
- 长连接监听工具状态变化
- 客户端支持 EventSource 的情况

## 注意事项

1. **传输实例隔离**: 每个连接都有独立的传输实例，确保会话安全
2. **HTTP 方法**: 使用 POST 方法，符合 MCP Streamable HTTP 规范
3. **正确的方法名**: 使用 `transport.connect()` 而不是 `transport.connect_http()`
4. **标签名称区分大小写**
5. **只返回启用状态的工具**
6. **如果标签不存在，返回空列表而不是错误**
7. **支持与现有 MCP 客户端的完全兼容性**

## 相关文件

- `api/routers/mcp_stream_router.py`: 主要实现文件
- `api/routers/mcp_sse_router.py`: SSE 实现参考
- `api/services/tool_service.py`: 工具查询服务
- `api/services/tag_service.py`: 标签查询服务
- `api/tests/test_mcp_stream_router.py`: 单元测试套件
- `test_stream_fix.py`: 功能测试脚本
- `test_stream_endpoint.py`: HTTP 端点测试脚本

## 集成到主应用

路由器已自动集成到主应用中：
```python
# api/main.py
from api.routers import mcp_stream_router
app.include_router(mcp_stream_router.router)
```

现在您可以通过以下端点访问 MCP Streamable HTTP 服务：
- `POST /stream` - 所有工具
- `POST /stream-{tag}` - 标签过滤的工具
